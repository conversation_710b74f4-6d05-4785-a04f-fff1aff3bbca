<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S3 签名测试</title>
</head>
<body>
    <h1>S3 签名算法测试</h1>
    <div id="result"></div>

    <script>
        // 简化的S3签名测试
        async function testS3Signature() {
            const result = document.getElementById('result');
            
            try {
                // 测试SHA256
                const testMessage = 'Hello World';
                const msgBuffer = new TextEncoder().encode(testMessage);
                const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
                const hashArray = Array.from(new Uint8Array(hashBuffer));
                const hash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
                
                result.innerHTML += `<p>SHA256('${testMessage}') = ${hash}</p>`;
                
                // 测试HMAC-SHA256
                const key = 'test-key';
                const keyBuffer = new TextEncoder().encode(key);
                const cryptoKey = await crypto.subtle.importKey(
                    'raw', keyBuffer, { name: 'HMAC', hash: 'SHA-256' }, false, ['sign']
                );
                const signature = await crypto.subtle.sign('HMAC', cryptoKey, msgBuffer);
                const sigArray = Array.from(new Uint8Array(signature));
                const hmacHash = sigArray.map(b => b.toString(16).padStart(2, '0')).join('');
                
                result.innerHTML += `<p>HMAC-SHA256('${key}', '${testMessage}') = ${hmacHash}</p>`;
                
                result.innerHTML += '<p style="color: green;">✅ S3签名算法基础功能测试通过</p>';
                
            } catch (error) {
                result.innerHTML += `<p style="color: red;">❌ 测试失败: ${error.message}</p>`;
            }
        }
        
        // 页面加载后运行测试
        window.addEventListener('load', testS3Signature);
    </script>
</body>
</html>
