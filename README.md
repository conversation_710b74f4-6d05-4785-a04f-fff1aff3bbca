# NodeSeek 私信优化脚本


## ✨ 功能特性

- 🗄️ **本地缓存**：使用 IndexedDB 本地存储聊天列表
- ☁️ **WebDAV 备份**：支持将聊天列表备份到 WebDAV 服务器
- 📱 **历史查看**：便捷查看历史聊天用户
- 🔄 **自动备份**：自动备份聊天列表数据
- 🔧 **配置管理**：简单易用的 WebDAV 配置界面
- 🎯 **智能过滤**：支持显示/隐藏最新聊天列表

## 🚀 安装方法

### 1. 安装用户脚本管理器

首先需要安装一个用户脚本管理器：

- **Chrome/Edge**: [Tampermonkey](https://chromewebstore.google.com/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
- **Firefox**: [Tampermonkey](https://addons.mozilla.org/en-US/firefox/addon/tampermonkey/) 或 [Greasemonkey](https://addons.mozilla.org/en-US/firefox/addon/greasemonkey/)
- **Safari**: [Tampermonkey](https://apps.apple.com/us/app/tampermonkey/id1482490089)

### 2. 安装脚本

1. 点击链接，[NodeSeek 私信优化脚本](https://greasyfork.org/zh-CN/scripts/544084-nodeseek-%E7%A7%81%E4%BF%A1%E4%BC%98%E5%8C%96%E8%84%9A%E6%9C%AC) Tampermonkey 会自动识别并提示安装。

## 📖 使用说明

### 基本使用

1. 安装脚本后，访问 [NodeSeek 私信页面](https://www.nodeseek.com/notification#/message?mode=list)
2. 脚本会自动开始工作，在私信链接旁边会出现"历史私信"按钮
3. 点击"历史私信"按钮可以查看所有历史聊天列表

### WebDAV 配置

1. 点击"历史私信"按钮打开历史记录窗口
2. 点击"WebDAV设置"按钮
3. 填写 WebDAV 服务器信息：
   - **服务器地址**：如 `https://dav.jianguoyun.com/dav/`
   - **用户名**：您的 WebDAV 用户名
   - **密码**：您的 WebDAV 密码
   - **备份路径**：备份文件存储路径，如 `/nodeseek_messages_backup/`

### 支持的 WebDAV 服务

- 坚果云
- 其他标准 WebDAV 服务(未测试)

## 🔧 功能详解

### 自动备份

- 页面重新激活时检查是否需要备份
- 聊天记录更新自动备份
- 手动点击"立即备份"按钮可强制备份

### 历史记录管理

- 支持查看所有历史聊天列表
- 可选择显示/隐藏最新聊天列表
- 点击"打开对话"可直接跳转到对应聊天

### 数据恢复

- 点击"从WebDAV恢复"可从备份中恢复数据
- 自动清理超过 30 个的旧备份文件

## ⚙️ 配置选项

脚本提供以下配置选项：

- **调试模式**：可通过菜单命令开启/关闭调试日志
- **显示最新聊天**：控制是否在历史记录中显示最新聊天
- **WebDAV 配置**：完整的 WebDAV 服务器配置


## 📝 注意事项

1. **登录状态**：使用前请确保已登录 NodeSeek 账户
2. **网络连接**：WebDAV 备份需要稳定的网络连接
3. **权限设置**：确保 WebDAV 服务器有读写权限
4. **数据安全**：建议定期检查备份文件的完整性

## 🐛 常见问题

### Q: 脚本无法正常工作？
A: 请检查：
- 是否已登录 NodeSeek 账户
- 用户脚本管理器是否正常运行
- 浏览器控制台是否有错误信息

### Q: WebDAV 备份失败？
A: 请检查：
- WebDAV 服务器地址是否正确
- 用户名和密码是否正确
- 备份路径是否存在且有写入权限
- 网络连接是否稳定

### Q: 历史记录显示不全？
A: 可能原因：
- 脚本刚安装，需要时间积累数据
- 本地数据库可能被清理，可尝试从 WebDAV 恢复

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 GitHub Issue
- NodeSeek 站内私信

---

**免责声明**：本脚本仅供学习和个人使用，请遵守 NodeSeek 网站的使用条款。
