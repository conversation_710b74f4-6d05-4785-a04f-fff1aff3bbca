# NodeSeek 私信优化脚本


## ✨ 功能特性

- 🗄️ **本地缓存**：使用 IndexedDB 本地存储聊天列表
- ☁️ **双重备份**：支持 WebDAV 和 S3 对象存储两种备份方式
- 📱 **历史查看**：便捷查看历史聊天用户
- 🔄 **自动备份**：自动备份聊天列表数据（每6小时）
- 🔧 **配置管理**：简单易用的备份配置界面
- 🎯 **智能过滤**：支持显示/隐藏最新聊天列表
- 🔐 **多平台支持**：兼容各种 S3 兼容的对象存储服务

## 🚀 安装方法

### 1. 安装用户脚本管理器

首先需要安装一个用户脚本管理器：

- **Chrome/Edge**: [Tampermonkey](https://chromewebstore.google.com/detail/tampermonkey/dhdgffkkebhmkfjojejmpbldmpobfkfo)
- **Firefox**: [Tampermonkey](https://addons.mozilla.org/en-US/firefox/addon/tampermonkey/) 或 [Greasemonkey](https://addons.mozilla.org/en-US/firefox/addon/greasemonkey/)
- **Safari**: [Tampermonkey](https://apps.apple.com/us/app/tampermonkey/id1482490089)

### 2. 安装脚本

1. 点击链接，[NodeSeek 私信优化脚本](https://greasyfork.org/zh-CN/scripts/544084-nodeseek-%E7%A7%81%E4%BF%A1%E4%BC%98%E5%8C%96%E8%84%9A%E6%9C%AC) Tampermonkey 会自动识别并提示安装。

## 📖 使用说明

### 基本使用

1. 安装脚本后，访问 [NodeSeek 私信页面](https://www.nodeseek.com/notification#/message?mode=list)
2. 脚本会自动开始工作，在私信链接旁边会出现"历史私信"按钮
3. 点击"历史私信"按钮可以查看所有历史聊天列表

### 备份配置

1. 点击"历史私信"按钮打开历史记录窗口
2. 点击"备份设置"按钮
3. 选择存储类型（WebDAV 或 S3）
4. 填写相应的配置信息

#### WebDAV 配置
- **服务器地址**：如 `https://dav.jianguoyun.com/dav/`
- **用户名**：您的 WebDAV 用户名
- **密码**：您的 WebDAV 密码
- **备份路径**：备份文件存储路径，如 `/nodeseek_messages_backup/`

#### S3 配置
- **S3 端点 (Endpoint)**：S3 服务端点 URL，如 `https://s3.amazonaws.com`
- **Access Key ID**：S3 访问密钥 ID
- **Secret Access Key**：S3 访问密钥
- **区域 (Region)**：S3 存储区域，如 `us-east-1`
- **存储桶 (Bucket)**：S3 存储桶名称
- **路径前缀**：可选的路径前缀，如 `nodeseek-backups/`

### 支持的存储服务

#### WebDAV 服务
- 坚果云
- NextCloud
- ownCloud
- Seafile
- 其他标准 WebDAV 服务

#### S3 兼容服务
- AWS S3
- 阿里云对象存储 OSS
- 腾讯云对象存储 COS
- 华为云对象存储 OBS
- 七牛云对象存储
- MinIO
- 其他兼容 S3 API 的对象存储服务

## 🔧 功能详解

### 自动备份

- 页面重新激活时检查是否需要备份
- 聊天记录更新自动备份
- 手动点击"立即备份"按钮可强制备份

### 历史记录管理

- 支持查看所有历史聊天列表
- 可选择显示/隐藏最新聊天列表
- 点击"打开对话"可直接跳转到对应聊天

### 数据恢复

- 点击"恢复备份"可从云端恢复数据到本地
- 支持从 WebDAV 或 S3 恢复（根据当前配置的存储类型）
- 自动清理超过 30 个的旧备份文件

## ⚙️ 配置选项

脚本提供以下配置选项：

- **存储类型选择**：可选择 WebDAV 或 S3 作为备份存储
- **调试模式**：可通过菜单命令开启/关闭调试日志
- **显示最新聊天**：控制是否在历史记录中显示最新聊天
- **备份配置**：完整的 WebDAV 或 S3 配置管理


## 📝 注意事项

1. **登录状态**：使用前请确保已登录 NodeSeek 账户
2. **网络连接**：WebDAV 备份需要稳定的网络连接
3. **权限设置**：确保 WebDAV 服务器有读写权限
4. **数据安全**：建议定期检查备份文件的完整性

## 🐛 常见问题

### Q: 脚本无法正常工作？
A: 请检查：
- 是否已登录 NodeSeek 账户
- 用户脚本管理器是否正常运行
- 浏览器控制台是否有错误信息

### Q: 备份失败？
A: 请检查：

**WebDAV 备份失败**：
- WebDAV 服务器地址是否正确
- 用户名和密码是否正确
- 备份路径是否存在且有写入权限
- 网络连接是否稳定

**S3 备份失败**：
- Access Key ID 和 Secret Access Key 是否正确
- 存储桶名称和区域是否正确
- 存储桶是否有读写权限
- 网络是否能访问 S3 服务

### Q: 历史记录显示不全？
A: 可能原因：
- 脚本刚安装，需要时间积累数据
- 本地数据库可能被清理，可尝试从 WebDAV 恢复

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 GitHub Issue
- NodeSeek 站内私信

---

**免责声明**：本脚本仅供学习和个人使用，请遵守 NodeSeek 网站的使用条款。
