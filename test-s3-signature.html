<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>S3 签名测试</title>
</head>
<body>
    <h1>S3 签名算法测试</h1>
    <div id="result"></div>

    <script>
        // S3签名调试工具
        async function testS3Signature() {
            const result = document.getElementById('result');

            try {
                // 测试SHA256
                const testMessage = 'Hello World';
                const msgBuffer = new TextEncoder().encode(testMessage);
                const hashBuffer = await crypto.subtle.digest('SHA-256', msgBuffer);
                const hashArray = Array.from(new Uint8Array(hashBuffer));
                const hash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

                result.innerHTML += `<p>SHA256('${testMessage}') = ${hash}</p>`;

                // 测试HMAC-SHA256
                const key = 'test-key';
                const keyBuffer = new TextEncoder().encode(key);
                const cryptoKey = await crypto.subtle.importKey(
                    'raw', keyBuffer, { name: 'HMAC', hash: 'SHA-256' }, false, ['sign']
                );
                const signature = await crypto.subtle.sign('HMAC', cryptoKey, msgBuffer);
                const sigArray = Array.from(new Uint8Array(signature));
                const hmacHash = sigArray.map(b => b.toString(16).padStart(2, '0')).join('');

                result.innerHTML += `<p>HMAC-SHA256('${key}', '${testMessage}') = ${hmacHash}</p>`;

                result.innerHTML += '<p style="color: green;">✅ S3签名算法基础功能测试通过</p>';

                // 添加S3调试信息
                result.innerHTML += `
                    <hr>
                    <h3>S3 403错误调试建议</h3>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;">
                        <h4>常见403错误原因：</h4>
                        <ul>
                            <li><strong>时间同步问题</strong>：确保系统时间准确，S3对时间敏感</li>
                            <li><strong>访问密钥错误</strong>：检查Access Key ID和Secret Access Key</li>
                            <li><strong>权限不足</strong>：确保IAM用户有s3:PutObject, s3:GetObject, s3:ListBucket权限</li>
                            <li><strong>存储桶策略</strong>：检查存储桶的访问策略</li>
                            <li><strong>区域不匹配</strong>：确保区域设置正确</li>
                            <li><strong>端点URL错误</strong>：检查S3端点URL格式</li>
                            <li><strong>签名算法问题</strong>：确保使用正确的签名版本4算法</li>
                        </ul>

                        <h4>调试步骤：</h4>
                        <ol>
                            <li>在脚本中开启调试模式：<code>window.chatBackup.Utils.enableDebug()</code></li>
                            <li>查看浏览器控制台的详细错误信息</li>
                            <li>检查请求头中的Authorization和x-amz-date字段</li>
                            <li>验证Canonical Request的构建是否正确</li>
                            <li>确认String to Sign的格式</li>
                        </ol>

                        <h4>AWS CLI测试命令：</h4>
                        <pre style="background: #e9ecef; padding: 10px; border-radius: 3px;">
# 测试访问权限
aws s3 ls s3://your-bucket-name --region your-region

# 测试上传
echo "test" | aws s3 cp - s3://your-bucket-name/test.txt --region your-region
                        </pre>
                    </div>
                `;

            } catch (error) {
                result.innerHTML += `<p style="color: red;">❌ 测试失败: ${error.message}</p>`;
            }
        }

        // 页面加载后运行测试
        window.addEventListener('load', testS3Signature);
    </script>
</body>
</html>
