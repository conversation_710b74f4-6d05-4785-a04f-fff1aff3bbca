# S3 对象存储配置指南

## 概述

本脚本现在支持 S3 对象存储作为备份方式，兼容各种 S3 兼容的对象存储服务。

## 支持的服务

### 国外服务
- **AWS S3**: 亚马逊官方对象存储服务
- **MinIO**: 开源的 S3 兼容对象存储

### 国内服务
- **阿里云 OSS**: 阿里云对象存储服务
- **腾讯云 COS**: 腾讯云对象存储服务
- **华为云 OBS**: 华为云对象存储服务
- **七牛云**: 七牛云对象存储服务

## 配置步骤

### 1. AWS S3 配置

1. 登录 [AWS 控制台](https://console.aws.amazon.com/)
2. 创建 S3 存储桶
3. 创建 IAM 用户并获取访问密钥
4. 在脚本中填写配置：
   - **Access Key ID**: 您的 AWS 访问密钥 ID
   - **Secret Access Key**: 您的 AWS 访问密钥
   - **Region**: 存储桶所在区域（如 `us-east-1`）
   - **Bucket**: 存储桶名称
   - **Prefix**: 可选路径前缀

### 2. 阿里云 OSS 配置

1. 登录 [阿里云控制台](https://oss.console.aliyun.com/)
2. 创建 Bucket
3. 创建 AccessKey
4. 在脚本中填写配置：
   - **Access Key ID**: 您的 AccessKey ID
   - **Secret Access Key**: 您的 AccessKey Secret
   - **Region**: Bucket 所在区域（如 `oss-cn-hangzhou`）
   - **Bucket**: Bucket 名称
   - **Prefix**: 可选路径前缀

### 3. 腾讯云 COS 配置

1. 登录 [腾讯云控制台](https://console.cloud.tencent.com/cos5)
2. 创建存储桶
3. 创建 API 密钥
4. 在脚本中填写配置：
   - **Access Key ID**: 您的 SecretId
   - **Secret Access Key**: 您的 SecretKey
   - **Region**: 存储桶所在区域（如 `ap-beijing`）
   - **Bucket**: 存储桶名称
   - **Prefix**: 可选路径前缀

## 权限配置

### AWS S3 权限策略示例

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::your-bucket-name",
                "arn:aws:s3:::your-bucket-name/*"
            ]
        }
    ]
}
```

### 阿里云 OSS 权限配置

确保 AccessKey 具有以下权限：
- `oss:GetObject`
- `oss:PutObject`
- `oss:DeleteObject`
- `oss:ListObjects`

## 注意事项

1. **安全性**: 请妥善保管访问密钥，不要泄露给他人
2. **费用**: 使用对象存储服务可能产生费用，请了解相关计费规则
3. **网络**: 确保网络能够访问相应的对象存储服务
4. **区域**: 选择合适的存储区域以获得更好的访问速度

## 故障排除

### 常见错误

1. **403 Forbidden**: 检查访问密钥和权限配置
2. **404 Not Found**: 检查存储桶名称和区域是否正确
3. **SignatureDoesNotMatch**: 检查访问密钥是否正确
4. **网络超时**: 检查网络连接或更换存储区域

### 调试方法

1. 在浏览器控制台开启调试模式：
   ```javascript
   window.chatBackup.Utils.enableDebug();
   ```

2. 查看详细的错误信息和请求日志

## 迁移指南

### 从 WebDAV 迁移到 S3

1. 在脚本配置中选择 S3 存储类型
2. 填写 S3 配置信息
3. 执行一次手动备份以验证配置
4. 原有的 WebDAV 备份文件不会自动迁移，需要手动处理

### 在不同 S3 服务间迁移

1. 在新的 S3 服务中创建存储桶
2. 更新脚本中的 S3 配置
3. 执行备份以在新服务中创建备份文件
4. 可选择性地删除旧服务中的备份文件

## 最佳实践

1. **定期检查**: 定期检查备份文件的完整性
2. **多重备份**: 可以同时使用 WebDAV 和 S3 进行双重备份
3. **监控费用**: 定期检查对象存储的使用费用
4. **访问控制**: 使用最小权限原则配置访问密钥
