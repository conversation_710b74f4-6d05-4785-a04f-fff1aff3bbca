# S3 调试指南

## 问题诊断

### 1. 网络错误 "S3上传网络错误: 未知错误"

这个错误通常表示网络请求被阻止或无法到达S3端点。

#### 可能原因：
1. **CORS 策略阻止**: 浏览器的跨域策略阻止了请求
2. **网络连接问题**: 无法访问S3端点
3. **端点URL错误**: S3端点配置不正确
4. **防火墙/代理阻止**: 网络环境阻止了S3请求

#### 解决方案：

##### 1. 检查S3端点配置
确保S3端点URL格式正确：
```
正确格式：
- AWS S3: https://s3.amazonaws.com
- 阿里云OSS: https://oss-cn-hangzhou.aliyuncs.com
- 腾讯云COS: https://cos.ap-beijing.myqcloud.com
- 自建MinIO: https://your-domain.com:9000

错误格式：
- 缺少协议: s3.amazonaws.com
- 多余的路径: https://s3.amazonaws.com/bucket
```

##### 2. 配置S3存储桶CORS
在S3存储桶中添加CORS配置：

**AWS S3 CORS配置：**
```json
[
    {
        "AllowedHeaders": ["*"],
        "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
        "AllowedOrigins": ["*"],
        "ExposeHeaders": ["ETag"]
    }
]
```

**阿里云OSS CORS配置：**
```xml
<CORSConfiguration>
    <CORSRule>
        <AllowedOrigin>*</AllowedOrigin>
        <AllowedMethod>GET</AllowedMethod>
        <AllowedMethod>PUT</AllowedMethod>
        <AllowedMethod>POST</AllowedMethod>
        <AllowedMethod>DELETE</AllowedMethod>
        <AllowedMethod>HEAD</AllowedMethod>
        <AllowedHeader>*</AllowedHeader>
        <ExposeHeader>ETag</ExposeHeader>
    </CORSRule>
</CORSConfiguration>
```

##### 3. 检查网络连接
1. 在浏览器中直接访问S3端点URL
2. 使用curl测试连接：
   ```bash
   curl -I https://your-s3-endpoint.com
   ```

##### 4. 使用S3连接测试功能
在脚本配置页面点击"测试S3连接"按钮，查看详细错误信息。

### 2. 403 权限错误

#### 可能原因：
1. **访问密钥错误**: Access Key ID 或 Secret Access Key 不正确
2. **权限不足**: IAM用户没有足够的S3权限
3. **存储桶策略**: 存储桶策略阻止了访问
4. **签名算法问题**: AWS签名计算错误

#### 解决方案：

##### 1. 验证访问密钥
确保Access Key ID和Secret Access Key正确且有效。

##### 2. 检查IAM权限
确保IAM用户具有以下权限：
```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "s3:GetObject",
                "s3:PutObject",
                "s3:DeleteObject",
                "s3:ListBucket"
            ],
            "Resource": [
                "arn:aws:s3:::your-bucket-name",
                "arn:aws:s3:::your-bucket-name/*"
            ]
        }
    ]
}
```

##### 3. 检查存储桶策略
确保存储桶策略允许访问。

##### 4. 时间同步
确保系统时间准确，S3签名对时间敏感。

### 3. 调试步骤

#### 1. 开启调试模式
在浏览器控制台执行：
```javascript
window.chatBackup.Utils.enableDebug();
```

#### 2. 查看详细日志
执行备份操作后，在控制台查看：
- Canonical Request
- String to Sign
- Signature
- Authorization header
- 请求URL和头部
- 响应状态和内容

#### 3. 使用S3连接测试
在配置页面使用"测试S3连接"功能，获取详细错误信息。

#### 4. 对比工作配置
如果有其他工具能正常访问S3，对比配置差异。

### 4. 常见S3服务配置

#### AWS S3
```
端点: https://s3.amazonaws.com
区域: us-east-1, us-west-2, eu-west-1 等
```

#### 阿里云OSS
```
端点: https://oss-cn-hangzhou.aliyuncs.com
区域: oss-cn-hangzhou, oss-cn-beijing 等
```

#### 腾讯云COS
```
端点: https://cos.ap-beijing.myqcloud.com
区域: ap-beijing, ap-shanghai 等
```

#### MinIO
```
端点: https://your-domain.com:9000
区域: 通常为 us-east-1
```

### 5. 故障排除清单

- [ ] S3端点URL格式正确
- [ ] Access Key ID和Secret Access Key正确
- [ ] 区域设置正确
- [ ] 存储桶名称正确
- [ ] 存储桶存在且可访问
- [ ] IAM权限配置正确
- [ ] 存储桶CORS配置正确
- [ ] 网络可以访问S3端点
- [ ] 系统时间准确
- [ ] 防火墙/代理不阻止S3请求

### 6. 获取帮助

如果问题仍然存在：
1. 开启调试模式收集详细日志
2. 使用S3连接测试功能
3. 检查浏览器网络面板的请求详情
4. 查看S3服务提供商的文档和支持

### 7. 替代方案

如果S3无法正常工作，可以：
1. 使用WebDAV备份
2. 使用本地导出功能
3. 尝试其他S3兼容服务
