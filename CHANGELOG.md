# 更新日志

## v1.1.0 - 2024-07-30

### 🎉 新增功能
- **S3 对象存储支持**: 新增对 S3 兼容对象存储的完整支持
- **存储类型选择**: 用户可以选择使用 WebDAV 或 S3 作为备份存储
- **多平台兼容**: 支持 AWS S3、阿里云 OSS、腾讯云 COS 等多种 S3 兼容服务
- **AWS 签名 v4**: 实现了完整的 AWS 签名版本 4 算法

### 🔧 改进优化
- **统一配置界面**: 重新设计了备份配置界面，支持 WebDAV 和 S3 配置切换
- **错误处理增强**: 改进了错误提示信息，针对不同存储类型提供更准确的错误描述
- **用户体验优化**: 更新了按钮文本和界面提示，使功能更加清晰
- **代码结构优化**: 重构了备份相关代码，提高了可维护性

### 🛠️ 技术实现
- **S3Backup 类**: 新增专门处理 S3 操作的类
- **AWS 签名算法**: 实现了 SHA256、HMAC-SHA256 和 AWS 签名 v4 算法
- **统一接口**: 为 WebDAV 和 S3 提供了统一的操作接口
- **配置管理**: 改进了配置存储和管理机制

### 📝 文档更新
- **README.md**: 更新了功能介绍和使用说明
- **S3_SETUP_GUIDE.md**: 新增 S3 配置详细指南
- **测试文件**: 添加了 S3 签名算法测试页面

### 🔒 安全性
- **密钥保护**: S3 访问密钥使用安全的本地存储
- **权限最小化**: 建议使用最小权限原则配置 S3 访问权限
- **签名安全**: 使用标准的 AWS 签名 v4 算法确保请求安全

### 🌐 兼容性
- **浏览器支持**: 兼容所有支持 Web Crypto API 的现代浏览器
- **用户脚本管理器**: 兼容 Tampermonkey、Violentmonkey 等主流用户脚本管理器
- **S3 服务**: 兼容所有支持 S3 API 的对象存储服务

### 📋 使用说明
1. 更新到 v1.1.0 版本
2. 点击"备份配置"按钮
3. 选择存储类型（WebDAV 或 S3）
4. 填写相应的配置信息
5. 保存配置并测试备份功能

### ⚠️ 注意事项
- 现有的 WebDAV 配置和备份文件不受影响
- S3 配置是独立的，不会覆盖 WebDAV 配置
- 可以随时在 WebDAV 和 S3 之间切换存储类型
- 建议在切换存储类型前先进行一次备份测试

---

## v1.0.0 - 2024-07-29

### 🎉 初始版本
- **本地缓存**: 使用 IndexedDB 存储聊天记录
- **WebDAV 备份**: 支持 WebDAV 服务器备份
- **历史记录**: 查看历史聊天记录功能
- **自动备份**: 定时自动备份功能
- **数据恢复**: 从 WebDAV 恢复备份功能
- **用户界面**: 简洁易用的操作界面
